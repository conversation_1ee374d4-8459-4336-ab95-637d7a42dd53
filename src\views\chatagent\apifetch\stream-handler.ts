/*
 * Copyright 2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/**
 * 流式响应处理器
 * 专门处理SSE（Server-Sent Events）和其他流式响应
 */

import type { RequestConfig, StreamHandler, SSEEvent, DirectStreamEvent } from './types'
import { FetchWrapper } from './fetch-wrapper'

export class StreamResponseHandler {
  private fetchWrapper: FetchWrapper

  constructor(fetchWrapper: FetchWrapper) {
    this.fetchWrapper = fetchWrapper
  }

  /**
   * 处理SSE流式响应
   */
  async handleSSEStream<T = any>(
    config: RequestConfig,
    handler: StreamH<PERSON><PERSON><T>
  ): Promise<void> {
    try {
      console.log('🚀 开始SSE流式请求:', config)

      // 设置SSE相关的请求头
      const sseConfig: RequestConfig = {
        ...config,
        headers: {
          ...config.headers,
          'Accept': 'text/event-stream',
          'Cache-Control': 'no-cache'
        }
      }

      // 执行请求
      const response = await this.fetchWrapper.request(sseConfig)
      
      if (!response.data || !(response.data instanceof Response)) {
        throw new Error('无效的流式响应')
      }

      const reader = response.data.body?.getReader()
      if (!reader) {
        throw new Error('无法获取响应流')
      }

      console.log('✅ SSE连接建立成功，开始读取流式数据')

      const decoder = new TextDecoder()
      let buffer = '' // 用于累积不完整的数据

      // 处理流式响应
      const readStream = async (): Promise<void> => {
        try {
          const { done, value } = await reader.read()

          if (done) {
            console.log('✅ SSE流式响应完成')
            
            // 处理缓冲区中剩余的数据
            if (buffer.trim()) {
              this.processSSEChunk(buffer, handler)
            }
            
            handler.onComplete()
            return
          }

          // 解码数据并添加到缓冲区
          const chunk = decoder.decode(value, { stream: true })
          buffer += chunk

          console.log('🔄 收到SSE数据块:', chunk)

          // 按行分割处理完整的事件
          const lines = buffer.split('\n')
          
          // 保留最后一行（可能不完整）
          buffer = lines.pop() || ''

          // 处理完整的行
          for (const line of lines) {
            if (line.trim()) {
              this.processSSEChunk(line + '\n', handler)
            }
          }

          // 继续读取下一块数据
          return readStream()
        } catch (error) {
          console.error('❌ 读取SSE流式响应失败:', error)
          handler.onError(error as Error)
        }
      }

      await readStream()
    } catch (error) {
      console.error('❌ SSE流式请求失败:', error)
      handler.onError(error as Error)
    }
  }

  /**
   * 处理SSE数据块
   */
  private processSSEChunk<T>(chunk: string, handler: StreamHandler<T>): void {
    console.log('🔍 处理SSE数据块:', chunk)

    const lines = chunk.split('\n')
    let eventData: Partial<SSEEvent> = {}

    for (const line of lines) {
      const trimmedLine = line.trim()

      if (trimmedLine.startsWith('data:')) {
        const data = trimmedLine.substring(5).trim()
        eventData.data = data

        if (data && data !== '') {
          try {
            console.log('📨 解析SSE事件数据:', data)
            const parsedData = JSON.parse(data)
            handler.onEvent(parsedData)
          } catch (e) {
            console.error('❌ 解析SSE事件失败:', e, data)
            // 如果JSON解析失败，直接传递原始数据
            handler.onEvent(data as any)
          }
        }
      } else if (trimmedLine.startsWith('event:')) {
        eventData.event = trimmedLine.substring(6).trim()
      } else if (trimmedLine.startsWith('id:')) {
        eventData.id = trimmedLine.substring(3).trim()
      } else if (trimmedLine.startsWith('retry:')) {
        const retryValue = trimmedLine.substring(6).trim()
        eventData.retry = parseInt(retryValue, 10)
      } else if (trimmedLine === '') {
        // 空行表示事件结束，这是SSE标准格式
        eventData = {}
      } else {
        // 其他格式的数据
        console.log('📋 SSE元数据:', trimmedLine)
      }
    }
  }

  /**
   * 处理直接流式响应（非SSE格式）
   */
  async handleDirectStream<T = any>(
    config: RequestConfig,
    handler: StreamHandler<T>
  ): Promise<void> {
    try {
      console.log('🚀 开始直接流式请求:', config)

      const response = await this.fetchWrapper.request(config)
      
      if (!response.data || !(response.data instanceof Response)) {
        throw new Error('无效的流式响应')
      }

      const reader = response.data.body?.getReader()
      if (!reader) {
        throw new Error('无法获取响应流')
      }

      console.log('✅ 直接流连接建立成功，开始读取流式数据')

      const decoder = new TextDecoder()

      // 处理流式响应
      const readStream = async (): Promise<void> => {
        try {
          const { done, value } = await reader.read()

          if (done) {
            console.log('✅ 直接流式响应完成')
            handler.onComplete()
            return
          }

          // 解码数据
          const chunk = decoder.decode(value, { stream: true })
          console.log('🔄 收到直接流数据块:', chunk)

          try {
            // 尝试解析为JSON
            const parsedData = JSON.parse(chunk)
            handler.onEvent(parsedData)
          } catch (e) {
            // 如果不是JSON，直接传递原始数据
            handler.onEvent(chunk as any)
          }

          // 继续读取下一块数据
          return readStream()
        } catch (error) {
          console.error('❌ 读取直接流式响应失败:', error)
          handler.onError(error as Error)
        }
      }

      await readStream()
    } catch (error) {
      console.error('❌ 直接流式请求失败:', error)
      handler.onError(error as Error)
    }
  }

  /**
   * 处理分块传输编码的响应
   */
  async handleChunkedStream<T = any>(
    config: RequestConfig,
    handler: StreamHandler<T>,
    chunkProcessor?: (chunk: string) => T[]
  ): Promise<void> {
    try {
      console.log('🚀 开始分块流式请求:', config)

      const response = await this.fetchWrapper.request(config)
      
      if (!response.data || !(response.data instanceof Response)) {
        throw new Error('无效的流式响应')
      }

      const reader = response.data.body?.getReader()
      if (!reader) {
        throw new Error('无法获取响应流')
      }

      console.log('✅ 分块流连接建立成功，开始读取流式数据')

      const decoder = new TextDecoder()
      let buffer = ''

      // 处理流式响应
      const readStream = async (): Promise<void> => {
        try {
          const { done, value } = await reader.read()

          if (done) {
            console.log('✅ 分块流式响应完成')
            
            // 处理缓冲区中剩余的数据
            if (buffer.trim()) {
              this.processChunkedData(buffer, handler, chunkProcessor)
            }
            
            handler.onComplete()
            return
          }

          // 解码数据并添加到缓冲区
          const chunk = decoder.decode(value, { stream: true })
          buffer += chunk

          console.log('🔄 收到分块数据:', chunk)

          // 处理完整的数据块
          this.processChunkedData(buffer, handler, chunkProcessor)
          buffer = '' // 清空缓冲区

          // 继续读取下一块数据
          return readStream()
        } catch (error) {
          console.error('❌ 读取分块流式响应失败:', error)
          handler.onError(error as Error)
        }
      }

      await readStream()
    } catch (error) {
      console.error('❌ 分块流式请求失败:', error)
      handler.onError(error as Error)
    }
  }

  /**
   * 处理分块数据
   */
  private processChunkedData<T>(
    data: string,
    handler: StreamHandler<T>,
    chunkProcessor?: (chunk: string) => T[]
  ): void {
    try {
      if (chunkProcessor) {
        const events = chunkProcessor(data)
        events.forEach(event => handler.onEvent(event))
      } else {
        // 默认处理：尝试解析为JSON
        try {
          const parsedData = JSON.parse(data)
          handler.onEvent(parsedData)
        } catch (e) {
          // 如果不是JSON，直接传递原始数据
          handler.onEvent(data as any)
        }
      }
    } catch (error) {
      console.error('❌ 处理分块数据失败:', error)
      handler.onError(error as Error)
    }
  }

  /**
   * 创建流式请求的AbortController
   */
  createStreamController(): AbortController {
    return new AbortController()
  }

  /**
   * 取消流式请求
   */
  cancelStream(controller: AbortController, reason?: string): void {
    controller.abort()
    console.log('🛑 流式请求已取消:', reason || '用户取消')
  }
}
