<template>
  <div class="agent-container">
    <div class="agent-header"> 模型配置 </div>
    <div class="main">
      <div class="sider">
        <div class="sider-title"> AI来源 </div>
        <div class="sider-center">
          <p
            v-for="(item, index) in modalList"
            :key="item.id"
            @click="selectItem(index)"
            :class="{ active: selectedIndex === index }"
            ><span>{{ item.name }}</span>

            <el-popconfirm
              :icon="InfoFilled"
              title="确定要删除吗?"
              @confirm="handleDelete(item.id)"
              @cancel="cancelEvent"
            >
              <template #reference>
                <el-icon v-if="selectedIndex === index"><Close /></el-icon>
                <span v-else style="display: none"></span>
              </template>
            </el-popconfirm>
          </p>
        </div>
        <div class="sider-bottom">
          <el-button :icon="Plus" @click="handleOpenNameModal">新增</el-button>
        </div>
      </div>
      <div class="content">
        <el-form label-position="top" label-width="auto">
          <el-form-item label="API地址">
            <el-input v-model="modelForm.apiHost" />
          </el-form-item>
          <el-form-item label="密钥">
            <el-input
              v-model="modelForm.apiKey"
              type="password"
              :show-password="true"
            />
          </el-form-item>
          <el-form-item label="类型">
            <!-- <el-input v-model="modelForm.type" /> -->
            <el-select v-model="modelForm.type">
              <el-option
                v-for="item in modelTypeOptions"
                :label="item.label"
                :value="item.value"
                :key="item.value"
              />
              <!-- <el-option label="Zone two" value="beijing" /> -->
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <!-- <div class="menusTitle">AI来源</div>  modelTypeOptions
      <div v-for="item in modalList" class="menuItem" :key="item.id">
        <span>{{ item.name }}</span>
      </div> -->
    </div>

    <el-dialog
      v-model="isModalNameVisible"
      title="添加AI来源"
      width="500"
      :destroy-on-close="true"
    >
      <basic-form
        :formList="modalNameFormSchema"
        :isCreate="false"
        :formData="sourceFormData"
        :showSubmit="false"
        :check-strictly="true"
        :labelWidth="80"
        ref="sourceFormDataRef"
      />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="isModalNameVisible = false">取消</el-button>
          <el-button
            type="primary"
            :loading="modalNameloading"
            @click="handleModelName"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { InfoFilled, Plus } from '@element-plus/icons-vue';
  import { getConfigListApi, deleteAiSourceApi, addAiSourceApi } from '../../api/model';
  import { modalNameFormSchema, modelTypeOptions } from './data';

  const modalList = ref<any[]>([]);
  const selectedIndex = ref(0);
  const isModalNameVisible = ref(false);
  const sourceFormData = ref({
    name: '',
    type: '',
  });
  const sourceFormDataRef = ref();
  const modalNameloading = ref(false);

  const modelForm = ref({
    apiHost: '',
    apiKey: '',
    type: '',
  });

  const getList = async () => {
    // const data = [
    //   {
    //     id: '1935953479805784065',
    //     aiConfigId: null,
    //     name: 'text',
    //     apiHost: '123123',
    //     apiKey: '1223423',
    //     type: 'ollama',
    //     teamId: null,
    //     version: 2,
    //   },
    //   {
    //     id: '1935225944331603969',
    //     aiConfigId: null,
    //     name: '6',
    //     apiHost: 'sss',
    //     apiKey: 's',
    //     type: 'openai',
    //     teamId: null,
    //     version: 0,
    //   },
    //   {
    //     id: '1935225859447279618',
    //     aiConfigId: null,
    //     name: '2',
    //     apiHost: 'xss',
    //     apiKey: '',
    //     type: 'ollama',
    //     teamId: null,
    //     version: 7,
    //   },
    //   {
    //     id: '1935225830879875074',
    //     aiConfigId: null,
    //     name: '1',
    //     apiHost: '111222',
    //     apiKey: 'sswww',
    //     type: 'openai',
    //     teamId: null,
    //     version: 4,
    //   },
    //   {
    //     id: '1935150314902634498',
    //     aiConfigId: null,
    //     name: '666',
    //     apiHost: '88811',
    //     apiKey: '5454',
    //     type: 'ollama',
    //     teamId: null,
    //     version: 5,
    //   },
    //   {
    //     id: '2',
    //     aiConfigId: null,
    //     name: 'openai',
    //     apiHost: 'https://dashscope.aliyuncs.com/compatible-mode',
    //     apiKey: 'sk-adf45b9e1376495a894ed242650af233',
    //     type: 'openai',
    //     teamId: null,
    //     version: 1,
    //   },
    //   {
    //     id: '1',
    //     aiConfigId: null,
    //     name: 'ollama',
    //     apiHost: 'ollama',
    //     apiKey: '2',
    //     type: 'ollama',
    //     teamId: null,
    //     version: 0,
    //   },
    // ];
    // modalList.value = data;

    // console.log(modalList.value);
    const res = await getConfigListApi();
    modalList.value = res;
  };
  getList();
  const selectItem = (index) => {
    selectedIndex.value = index; // 更新选中的索引
  };
  const handleDelete = (id) => {
    console.log(id);

    deleteAiSourceApi(id).then(() => {
      getList();
      selectedIndex.value = 0;
      ElMessage({
        type: 'success',
        showClose: true,
        message: '删除成功！',
      });
    });
  };
  const cancelEvent = () => {
    console.log('cancel!');
  };
  const handleOpenNameModal = () => {
    isModalNameVisible.value = true;
    sourceFormData.value = {
      name: '',
      type: '',
    };
    // sourceForm.resetFields()
  };
  const handleModelName = () => {
    const getData = sourceFormDataRef.value.submitForm;
    const ruleFormRef = sourceFormDataRef.value.ruleFormRef;
    getData(ruleFormRef, (status, data) => {
      if (status == 'success') {
        modalNameloading.value = true;
        addAiSourceApi(data)
          .then(() => {
            ElMessage({
              type: 'success',
              message: `新增成功`,
            });
            modalNameloading.value = false;
            selectedIndex.value = 0;
            getList();
          })
          .catch(() => {
            modalNameloading.value = false;
          });
      }
    });
  };
</script>

<style scoped lang="scss">
  .agent-container {
    height: calc(100vh - 62px);
    border: 1px solid #e6e8ee;
    background-color: #fff;
    border-radius: 8px;
    margin: 0 8px 8px 0;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    .agent-header {
      border-bottom: 1px solid #e6e8ee;
      background: #fff;
      background-color: #ffffff;
      justify-content: space-between;
      align-items: center;
      height: 64px;
      padding: 16px 20px;
      display: flex;
      position: relative;
    }
    .main {
      display: flex;
      flex: 1;
      .sider {
        flex: 0 0 200px;
        border-right: 1px solid #e6e8ee;
        padding: 12px;
        display: flex;
        flex-direction: column;
        height: calc(100vh - 120px);
        .sider-title {
          line-height: 36px;
          margin-bottom: 12px;
          font-size: 16px;
          height: 36px;
        }
        .sider-center {
          flex: 1;
          overflow-y: auto;
          p.active {
            background: rgba(205, 208, 220, 0.8);
          }
          p {
            padding: 10px;
            cursor: pointer;
            border-radius: 4px;
            display: flex;
            justify-content: space-between;
            .el-icon {
              font-size: 16px;
              margin-top: 3px;
            }
          }
        }
        .sider-bottom {
          height: 30px;
          .el-button {
            width: 100%;
          }
        }
      }
      .content {
        flex: 1;
        overflow: auto;
        padding: 24px;
      }
    }
  }
</style>
