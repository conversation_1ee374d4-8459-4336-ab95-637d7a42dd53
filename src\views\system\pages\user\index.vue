<template>
  <div class="agent-container">
    <div class="agent-header"> 用户 </div>
    <div> content </div>
  </div>
</template>

<style scoped lang="scss">
  .agent-container {
    height: calc(100vh - 62px);
    border: 1px solid #e6e8ee;
    background-color: #fff;
    border-radius: 8px;
    margin: 0 8px 8px 0;
    overflow: hidden;
    .agent-header {
      border-bottom: 1px solid #e6e8ee;
      background: #fff;
      background-color: #ffffff;
      justify-content: space-between;
      align-items: center;
      height: 64px;
      padding: 16px 20px;
      display: flex;
      position: relative;
    }
  }
</style>
