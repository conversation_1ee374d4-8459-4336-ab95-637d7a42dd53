/* eslint-disable no-unused-vars */
/* eslint-disable no-shadow */
import { defHttp } from '/@/utils/axios';

enum Api {
  getConfigList = '/cpit/ai/setting/config/list',
  config = '/cpit/ai/setting/config',
}
export const getConfigListApi = () => {
  return defHttp.get({ url: Api.getConfigList });
};
// const deleteAiSource = createRequest<{ id: string }, boolean>('/cpit/ai/setting/config/:id', {
//   method: 'delete',
// });
export const deleteAiSourceApi = (id: string) =>
  defHttp.delete({ url: `${Api.config}/${id}` });

export const addAiSourceApi = (params) => defHttp.post({ url: Api.config, params });
