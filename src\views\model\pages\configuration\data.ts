export const modelTypeOptions = [
  {
    value: 'openai',
    label: 'openai',
  },
  {
    value: 'ollama',
    label: 'ollama',
  },
];

export const modalNameFormSchema: any[] = [
  {
    field: 'name',
    label: '名称',
    component: 'Input',
    componentProps: {
      placeholder: '请输入名称',
    },
    required: true,
  },
  {
    field: 'type',
    label: '类型',
    component: 'Select',
    componentProps: {
      placeholder: '请选择类型',
      options: modelTypeOptions,
    },
    required: true,
  },
];
